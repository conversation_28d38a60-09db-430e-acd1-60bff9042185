{"version": 3, "file": "config-loader.js", "sourceRoot": "", "sources": ["../src/config-loader.ts"], "names": [], "mappings": ";;;AAAA,mDAAqD;AACrD,2BAA6B;AAC7B,qCAAoC;AAsCpC,SAAgB,UAAU,CAAC,GAAyB;IAAzB,oBAAA,EAAA,MAAc,iBAAO,CAAC,GAAG;IAClD,OAAO,YAAY,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACpC,CAAC;AAFD,gCAEC;AAED,SAAgB,YAAY,CAAC,EAIR;QAHnB,GAAG,SAAA,EACH,cAAc,oBAAA,EACd,sBAA+C,EAA/C,cAAc,mBAAG,eAAe,CAAC,cAAc,KAAA;IAE/C,IAAI,cAAc,EAAE;QAClB,gDAAgD;QAChD,IAAM,iBAAe,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7D,CAAC,CAAC,cAAc,CAAC,OAAO;YACxB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;QAE3C,OAAO;YACL,UAAU,EAAE,SAAS;YACrB,sBAAsB,EAAE,EAAE;YAC1B,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,eAAe,mBAAA;YACf,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,WAAW,EAAE,cAAc,CAAC,WAAW;SACxC,CAAC;KACH;IAED,kDAAkD;IAClD,IAAM,UAAU,GAAG,cAAc,CAAC;QAChC,GAAG,KAAA;QACH,MAAM,EAAE,UAAC,GAAW,IAAK,OAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAhB,CAAgB;KAC1C,CAAC,CAAC;IAEH,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;QAC5B,OAAO;YACL,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,6BAA6B;SACvC,CAAC;KACH;IAED,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;QACvB,OAAO;YACL,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,oCAAoC;SAC9C,CAAC;KACH;IAED,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;IAC1D,IAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;IAEnE,OAAO;QACL,UAAU,EAAE,SAAS;QACrB,sBAAsB,EAAE,UAAU,CAAC,YAAY;QAC/C,OAAO,EAAE,UAAU,CAAC,OAAO;QAC3B,eAAe,iBAAA;QACf,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE;KAC9B,CAAC;AACJ,CAAC;AApDD,oCAoDC"}