{"author": {"name": "<PERSON>", "email": "<EMAIL>", "website": "https://github.com/stash"}, "contributors": [{"name": "<PERSON>", "website": "https://github.com/inikulin"}, {"name": "<PERSON><PERSON>", "website": "https://github.com/<PERSON><PERSON>"}, {"name": "<PERSON>", "website": "https://github.com/ruoho"}, {"name": "<PERSON>", "website": "https://github.com/ianlivingstone"}, {"name": "<PERSON>", "website": "https://github.com/awaterma"}, {"name": "<PERSON> ", "website": "https://github.com/medelibero-sfdc"}, {"name": "<PERSON>", "website": "https://github.com/jstewmon"}, {"name": "<PERSON>", "website": "https://github.com/miggs125"}, {"name": "<PERSON>", "website": "https://github.com/Sebmaster"}, {"name": "<PERSON>", "website": "https://github.com/apsavin"}, {"name": "<PERSON><PERSON>", "website": "https://github.com/lalitkapoor"}, {"name": "<PERSON>", "website": "https://github.com/sambthompson"}], "license": "BSD-3-<PERSON><PERSON>", "name": "tough-cookie", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "version": "4.1.4", "homepage": "https://github.com/salesforce/tough-cookie", "repository": {"type": "git", "url": "git://github.com/salesforce/tough-cookie.git"}, "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "main": "./lib/cookie", "files": ["lib"], "scripts": {"version": "genversion lib/version.js && git add lib/version.js", "test": "vows test/*_test.js && npm run eslint", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "eslint": "eslint --env node --ext .js .", "prettier": "prettier '**/*.{json,ts,yaml,md}'", "format": "npm run eslint -- --fix"}, "engines": {"node": ">=6"}, "devDependencies": {"async": "^2.6.2", "eslint": "^5.16.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1", "genversion": "^2.1.0", "nyc": "^14.0.0", "prettier": "^1.17.0", "vows": "^0.8.2"}, "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}}