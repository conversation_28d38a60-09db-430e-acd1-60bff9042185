# -*- coding: utf-8 -*-
"""
Dự đoán <PERSON> nhà trên Colab với Linear Regression, XGBoost và ANN,
so s<PERSON>h hi<PERSON><PERSON> n<PERSON>, và lưu mô hình/preprocessor.
"""

# === 1. CÀI ĐẶT THƯ VIỆN ===
!pip install xgboost tensorflow -q
print("Đã cài đặt XGBoost và TensorFlow.")

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from sklearn.model_selection import train_test_split, KFold, cross_val_score
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error

import xgboost as xgb
import tensorflow as tf
from tensorflow.keras import layers, models, callbacks
import joblib

from google.colab import files

# === 2. TẢI VÀ KHÁM PHÁ DỮ LIỆU ===
df = pd.read_csv('/content/drive/MyDrive/AI/train.csv')
print("Dữ liệu ban đầu:", df.shape)

# Loại bỏ outliers
df = df[df['GrLivArea'] < 4000]

# Phân phối SalePrice & Log(1+SalePrice)
df['SalePrice_Log'] = np.log1p(df['SalePrice'])
plt.figure(figsize=(12,4))
plt.subplot(1,2,1)
plt.hist(df['SalePrice'], bins=50); plt.title('SalePrice')
plt.subplot(1,2,2)
plt.hist(df['SalePrice_Log'], bins=50); plt.title('Log1p(SalePrice)')
plt.show()

# Giữ 5 mẫu để test cuối
sample_new = df.sample(5, random_state=101)

# === 3. TIỀN XỬ LÝ ===
X = df.drop(['Id','SalePrice','SalePrice_Log'], axis=1)
y = df['SalePrice_Log']

num_cols = X.select_dtypes(include=np.number).columns.tolist()
cat_cols = X.select_dtypes(include='object').columns.tolist()

num_pipe = Pipeline([
    ('imputer', SimpleImputer(strategy='median')),
    ('scaler', StandardScaler())
])
cat_pipe = Pipeline([
    ('imputer', SimpleImputer(strategy='constant', fill_value='missing')),
    ('onehot', OneHotEncoder(handle_unknown='ignore', sparse_output=False)) # Thay 'sparse' thành 'sparse_output'
])

preprocessor = ColumnTransformer([
    ('num', num_pipe, num_cols),
    ('cat', cat_pipe, cat_cols)
], remainder='passthrough')

X_proc = preprocessor.fit_transform(X)
print("Dữ liệu sau tiền xử lý:", X_proc.shape)

# Chia train/valid cho ANN và XGBoost early stopping
X_train, X_val, y_train, y_val = train_test_split(
    X_proc, y, test_size=0.2, random_state=42
)

# K-Fold setup cho CV
kf = KFold(n_splits=5, shuffle=True, random_state=42)

# === 4. LINEAR REGRESSION ===
print("\n--- Linear Regression (5‑fold CV) ---")
lr = LinearRegression()
lr_scores = -cross_val_score(
    lr, X_proc, y, cv=kf,
    scoring='neg_root_mean_squared_error', n_jobs=-1
)
print(f"LR RMSE (log scale): {lr_scores.mean():.5f} ± {lr_scores.std():.5f}")

# === 5. XGBOOST ===
print("\n--- XGBoost với Early Stopping ---")
xgb_tmp = xgb.XGBRegressor(
    objective='reg:squarederror',
    n_estimators=1000,
    learning_rate=0.05,
    max_depth=5,
    subsample=0.7,
    colsample_bytree=0.7,
    random_state=42,
    n_jobs=-1,
    early_stopping_rounds=50
)
#early_stop = EarlyStopping(rounds=50, metric_name='rmse', data_name='eval', save_best=True)
xgb_tmp.fit(
    X_train, y_train,
    eval_set=[(X_val, y_val)],
    #callbacks=[early_stop],
    verbose=False
)
best_n = xgb_tmp.best_iteration
print("Best n_estimators:", best_n)

# 2.4 Huấn luyện mô hình cuối cùng
xgb_final = xgb.XGBRegressor(
    objective='reg:squarederror',
    n_estimators=best_n,
    learning_rate=0.05,
    max_depth=5,
    subsample=0.7,
    colsample_bytree=0.7,
    random_state=42,
    n_jobs=-1
)
xgb_final.fit(X_train, y_train)  # No early stopping for final model

xgb_scores = -cross_val_score(
    xgb_final, X_proc, y, cv=kf,
    scoring='neg_root_mean_squared_error', n_jobs=-1
)
print(f"XGB RMSE (log scale): {xgb_scores.mean():.5f} ± {xgb_scores.std():.5f}")

# === 6. ANN (MLP) ===
print("\n--- ANN (Keras MLP) ---")
# Xây dựng mô hình
def build_mlp(input_dim):
    model = models.Sequential([
        layers.InputLayer(input_shape=(input_dim,)),
        layers.Dense(128, activation='relu'),
        layers.Dropout(0.2),
        layers.Dense(64, activation='relu'),
        layers.Dropout(0.2),
        layers.Dense(1)  # Dự đoán log(SalePrice)
    ])
    model.compile(
        optimizer='adam',
        loss='mse',
        metrics=[tf.keras.metrics.RootMeanSquaredError()]
    )
    return model

mlp = build_mlp(X_proc.shape[1])
es = callbacks.EarlyStopping(
    monitor='val_root_mean_squared_error',
    patience=10,
    restore_best_weights=True
)
history = mlp.fit(
    X_train, y_train,
    validation_data=(X_val, y_val),
    epochs=200,
    batch_size=32,
    callbacks=[es],
    verbose=1
)

# Đánh giá trên tập validation
val_metrics = mlp.evaluate(X_val, y_val, verbose=0)
mlp_rmse = val_metrics[1]
print(f"ANN RMSE trên validation (log scale): {mlp_rmse:.5f}")

# === 7. ĐÁNH GIÁ BẰNG RMSE VÀ RMSLE TRÊN TẬP VALIDATION ===
from sklearn.metrics import mean_squared_error

# 1) Huấn luyện lại mô hình trên X_train, y_train (nếu cần)
# Linear Regression
lr.fit(X_train, y_train)
# XGBoost (đã tìm best_n trước đó)
xgb_final.fit(X_train, y_train)
# ANN (mlp) đã được huấn luyện với early stopping, weights tốt nhất đang ở trong mlp

# 2) Dự đoán log(1+SalePrice)
lr_log_pred  = lr.predict(X_val)
xgb_log_pred = xgb_final.predict(X_val)
mlp_log_pred = mlp.predict(X_val).flatten()

# 3) Chuyển về thang gốc
y_true_log  = y_val
y_true_orig = np.expm1(y_val)

lr_pred_orig  = np.expm1(lr_log_pred)
xgb_pred_orig = np.expm1(xgb_log_pred)
mlp_pred_orig = np.expm1(mlp_log_pred)

# 4) Tính RMSE trên thang gốc
lr_rmse  = mean_squared_error(y_true_orig, lr_pred_orig)**0.5  # Tính căn bậc hai để có RMSE
xgb_rmse = mean_squared_error(y_true_orig, xgb_pred_orig)**0.5 # Tính căn bậc hai để có RMSE
mlp_rmse = mean_squared_error(y_true_orig, mlp_pred_orig)**0.5 # Tính căn bậc hai để có RMSE

# 5) Tính RMSLE = RMSE trên log1p
#    RMSLE = sqrt(mean((log_pred - y_true_log)^2))
lr_rmsle  = mean_squared_error(y_true_log, lr_log_pred)**0.5   # Tính căn bậc hai để có RMSLE
xgb_rmsle = mean_squared_error(y_true_log, xgb_log_pred)**0.5  # Tính căn bậc hai để có RMSLE
mlp_rmsle = mean_squared_error(y_true_log, mlp_log_pred)**0.5  # Tính căn bậc hai để có RMSLE

# 6) In kết quả
eval_df = pd.DataFrame({
    'Model':  ['Linear Regression', 'XGBoost', 'ANN (MLP)'],
    'RMSE (gốc)': [lr_rmse, xgb_rmse, mlp_rmse],
    'RMSLE':      [lr_rmsle, xgb_rmsle, mlp_rmsle]
})
print("\n=== Bảng đánh giá trên tập validation ===")
print(eval_df)

# 7) Vẽ biểu đồ so sánh
plt.figure(figsize=(8,4))
# RMSE
plt.subplot(1,2,1)
plt.bar(eval_df['Model'], eval_df['RMSE (gốc)'])
plt.title('RMSE (thang gốc)')
plt.xticks(rotation=15)
plt.ylabel('RMSE')

# RMSLE
plt.subplot(1,2,2)
plt.bar(eval_df['Model'], eval_df['RMSLE'])
plt.title('RMSLE (log scale)')
plt.xticks(rotation=15)
plt.ylabel('RMSLE')

plt.tight_layout()
plt.show()


# === 7. SO SÁNH KẾT QUẢ ===
results = pd.DataFrame({
    'Model': ['Linear Regression', 'XGBoost', 'ANN (MLP)'],
    'RMSE_log': [
        lr_scores.mean(),
        xgb_scores.mean(),
        mlp_rmse
    ],
    'Std_log': [
        lr_scores.std(),
        xgb_scores.std(),
        np.nan
    ]
})
print("\n=== Bảng so sánh ===")
print(results)

# Vẽ biểu đồ so sánh
plt.figure(figsize=(6,4))
plt.bar(results['Model'], results['RMSE_log'])
plt.ylabel('RMSE (log scale)')
plt.title('So sánh RMSE giữa các mô hình')
plt.xticks(rotation=15)
plt.tight_layout()
plt.show()

# === 8. LƯU MÔ HÌNH & PREPROCESSOR ===
print("\n--- Lưu mô hình & preprocessor ---")
# Chọn mô hình tốt nhất (ví dụ XGBoost)
best_model = xgb_final
best_model.fit(X_proc, y)  # Huấn luyện lại trên toàn bộ
joblib.dump(best_model, 'best_model.joblib')
joblib.dump(preprocessor, 'preprocessor.joblib')
print("Đã lưu 'best_model.joblib' và 'preprocessor.joblib'")

# === 9. DỰ ĐOÁN TRÊN test.csv ===
print("\n--- Dự đoán trên test.csv ---")
test_df = pd.read_csv('/content/drive/MyDrive/AI/test.csv')
ids = test_df['Id']
X_test = test_df.drop('Id', axis=1)
X_test_proc = preprocessor.transform(X_test)
log_preds = best_model.predict(X_test_proc)
final_preds = np.expm1(log_preds)

submission = pd.DataFrame({'Id': ids, 'SalePrice': final_preds})
submission.to_csv('submission.csv', index=False)
print("Đã tạo submission.csv")
print(submission.head())
"""
Hướng dẫn:
- RMSE tính trên log(1+SalePrice). Để chuyển về đơn vị gốc bạn dùng expm1.
- Bạn có thể thay đổi kiến trúc ANN hoặc hyper‑parameters.
- Kết quả so sánh nằm ở bảng “results” và biểu đồ.
"""
